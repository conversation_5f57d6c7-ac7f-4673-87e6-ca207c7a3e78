SELECT * FROM cust.cm_beisen_userinfo t where t.ext3='A00003' or t.ext2='000135';

SELECT t.curmonthsalary,t.beisenid,t.* FROM cust.cm_consultant_exp t where t.userid='felix.ye';

SELECT t.curmonthsalary_flag,t.* FROM cust.cm_consultant_exp_modify_flag t where t.userid  ='felix.ye';      
4.226
SELECT * FROM cust.CM_STOCK_SPLIT_COEFFICIENT t where t.cust_no='1180672577';


SELECT * FROM cust.cm_stock_split_config t 
where t.config_level='4' and t.former_cons_code ='fuxun.jiang'
and t.valid_flag = '1' and t.audit_status = '1' and t.rec_stat = '1'  and t.id='3079088';

select * from cust.cm_stock_split_detail ssd
where ssd.config_id='3079088'
and ssd.cust_no='1180672577'
and ssd.rec_stat = '1';

update cust.cm_stock_split_detail 
set upper_limit=20
where config_id='3079081'
and cust_no='1180672577'
and rec_stat = '1';

---------------------------------cal_start_dt < '20250301'的upper_limit历史数据要不要重刷---------------------------------
select * from cust.cm_stock_split_detail t
where exists (
select 1
FROM cust.cm_stock_split_config t1
join cust.cm_stock_split_detail t2
on t1.id = t2.config_id
join cust.CM_STOCK_SPLIT_COEFFICIENT t3
on t3.cust_no=t2.cust_no and t1.config_level=t3.config_level and t1.newly_cons_code=t3.cons_code and t3.data_month='202503'
WHERE t1.valid_flag = '1' and t1.audit_status = '1' and t1.rec_stat = '1' and t2.rec_stat = '1'
--and t1.cal_start_dt = '20250301'
and nvl(t2.upper_limit,0) != nvl(t3.rate,0)
and t.id = t2.id
);


